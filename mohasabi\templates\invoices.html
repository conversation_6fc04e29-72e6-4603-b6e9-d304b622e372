{% extends "base.html" %}

{% block title %}إدارة الفواتير - محاسبي{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-invoice me-2"></i>
                إدارة الفواتير
            </h1>
            <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات الفواتير -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ invoices|length }}</h4>
                        <p class="mb-0">إجمالي الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ invoices|selectattr('status', 'equalto', 'paid')|list|length }}</h4>
                        <p class="mb-0">فواتير مدفوعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ invoices|selectattr('status', 'equalto', 'sent')|list|length }}</h4>
                        <p class="mb-0">فواتير مرسلة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-paper-plane fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ invoices|selectattr('status', 'equalto', 'overdue')|list|length }}</h4>
                        <p class="mb-0">فواتير متأخرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أدوات البحث والتصفية -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control table-search" 
                           data-target="invoices-table" 
                           placeholder="البحث في الفواتير...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="draft">مسودة</option>
                    <option value="sent">مرسلة</option>
                    <option value="paid">مدفوعة</option>
                    <option value="overdue">متأخرة</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">من</span>
                    <input type="date" class="form-control" id="date-from">
                    <span class="input-group-text">إلى</span>
                    <input type="date" class="form-control" id="date-to">
                </div>
            </div>
            <div class="col-md-3">
                <div class="btn-group w-100">
                    <button class="btn btn-outline-secondary" onclick="exportToCSV()">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <button class="btn btn-outline-secondary" onclick="printPage()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary">قائمة الفواتير</h6>
    </div>
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-bordered sortable-table" id="invoices-table">
                <thead>
                    <tr>
                        <th data-sort="invoice_number" data-type="string">رقم الفاتورة</th>
                        <th data-sort="customer" data-type="string">العميل</th>
                        <th data-sort="issue_date" data-type="date">تاريخ الإصدار</th>
                        <th data-sort="due_date" data-type="date">تاريخ الاستحقاق</th>
                        <th data-sort="total_amount" data-type="number">المبلغ الإجمالي</th>
                        <th data-sort="status" data-type="string">الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td data-invoice_number="{{ invoice.invoice_number }}">
                            <strong>{{ invoice.invoice_number }}</strong>
                        </td>
                        <td data-customer="{{ invoice.customer.name if invoice.customer else '' }}">
                            {% if invoice.customer %}
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ invoice.customer.name[0] }}
                                    </div>
                                    {{ invoice.customer.name }}
                                </div>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td data-issue_date="{{ invoice.issue_date.strftime('%Y-%m-%d') if invoice.issue_date else '' }}">
                            {% if invoice.issue_date %}
                                {{ invoice.issue_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td data-due_date="{{ invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '' }}">
                            {% if invoice.due_date %}
                                {{ invoice.due_date.strftime('%Y-%m-%d') }}
                                {% if invoice.due_date < moment().date() and invoice.status != 'paid' %}
                                    <br><small class="text-danger">متأخرة</small>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td data-total_amount="{{ invoice.total_amount or 0 }}">
                            <strong class="currency">{{ "{:,.2f}".format(invoice.total_amount or 0) }} ر.س</strong>
                        </td>
                        <td data-status="{{ invoice.status }}">
                            {% if invoice.status == 'draft' %}
                                <span class="badge bg-secondary">مسودة</span>
                            {% elif invoice.status == 'sent' %}
                                <span class="badge bg-info">مرسلة</span>
                            {% elif invoice.status == 'paid' %}
                                <span class="badge bg-success">مدفوعة</span>
                            {% elif invoice.status == 'overdue' %}
                                <span class="badge bg-danger">متأخرة</span>
                            {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-dark">ملغية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="#" class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if invoice.status == 'draft' %}
                                <a href="#" class="btn btn-outline-success" title="تحرير">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <a href="#" class="btn btn-outline-info" title="طباعة PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                {% if invoice.status in ['draft', 'sent'] %}
                                <button class="btn btn-outline-warning" title="إرسال" 
                                        onclick="sendInvoice({{ invoice.id }})">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                                {% endif %}
                                {% if invoice.status != 'paid' %}
                                <button class="btn btn-outline-success" title="تسجيل دفعة" 
                                        onclick="recordPayment({{ invoice.id }})">
                                    <i class="fas fa-money-bill"></i>
                                </button>
                                {% endif %}
                                {% if invoice.status == 'draft' %}
                                <button class="btn btn-outline-danger" title="حذف" 
                                        onclick="deleteInvoice({{ invoice.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير</h5>
            <p class="text-muted">ابدأ بإنشاء فاتورتك الأولى</p>
            <a href="{{ url_for('add_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء أول فاتورة
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تصفية الفواتير حسب الحالة
document.getElementById('status-filter').addEventListener('change', function() {
    filterInvoices();
});

// تصفية الفواتير حسب التاريخ
document.getElementById('date-from').addEventListener('change', function() {
    filterInvoices();
});

document.getElementById('date-to').addEventListener('change', function() {
    filterInvoices();
});

function filterInvoices() {
    const status = document.getElementById('status-filter').value;
    const dateFrom = document.getElementById('date-from').value;
    const dateTo = document.getElementById('date-to').value;
    const rows = document.querySelectorAll('#invoices-table tbody tr');
    
    rows.forEach(row => {
        let show = true;
        
        // تصفية حسب الحالة
        if (status) {
            const invoiceStatus = row.querySelector('[data-status]').dataset.status;
            if (invoiceStatus !== status) {
                show = false;
            }
        }
        
        // تصفية حسب التاريخ
        if (dateFrom || dateTo) {
            const issueDate = row.querySelector('[data-issue_date]').dataset.issue_date;
            if (issueDate) {
                if (dateFrom && issueDate < dateFrom) {
                    show = false;
                }
                if (dateTo && issueDate > dateTo) {
                    show = false;
                }
            }
        }
        
        row.style.display = show ? '' : 'none';
    });
}

// إرسال فاتورة
function sendInvoice(invoiceId) {
    if (confirm('هل تريد إرسال هذه الفاتورة للعميل؟')) {
        fetch(`/invoices/${invoiceId}/send`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إرسال الفاتورة بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في إرسال الفاتورة', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ في إرسال الفاتورة', 'danger');
        });
    }
}

// تسجيل دفعة
function recordPayment(invoiceId) {
    const amount = prompt('أدخل مبلغ الدفعة:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        fetch(`/invoices/${invoiceId}/payment`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                amount: parseFloat(amount)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم تسجيل الدفعة بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في تسجيل الدفعة', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ في تسجيل الدفعة', 'danger');
        });
    }
}

// حذف فاتورة
function deleteInvoice(invoiceId) {
    if (confirmDelete('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        fetch(`/invoices/${invoiceId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الفاتورة بنجاح', 'success');
                location.reload();
            } else {
                showAlert('حدث خطأ في حذف الفاتورة', 'danger');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ في حذف الفاتورة', 'danger');
        });
    }
}

// تصدير البيانات
function exportToCSV() {
    const table = document.getElementById('invoices-table');
    const rows = table.querySelectorAll('tbody tr:not([style*="display: none"])');
    const data = [];
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        data.push({
            'رقم الفاتورة': cells[0].dataset.invoice_number,
            'العميل': cells[1].dataset.customer,
            'تاريخ الإصدار': cells[2].dataset.issue_date,
            'تاريخ الاستحقاق': cells[3].dataset.due_date,
            'المبلغ الإجمالي': cells[4].dataset.total_amount,
            'الحالة': cells[5].dataset.status
        });
    });
    
    MohasabiApp.exportToCSV(data, 'invoices.csv');
}
</script>
{% endblock %}
