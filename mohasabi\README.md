# محاسبي - نظام المحاسبة العربي
## Mohasabi - Arabic Accounting System

نظام محاسبة شامل مصمم خصيصاً للشركات الصغيرة والمتوسطة في المنطقة العربية، مع دعم كامل للغة العربية والمتطلبات المحلية.

## المميزات الرئيسية

### 📊 إدارة الفواتير
- إنشاء وتحرير الفواتير بسهولة
- دعم العملات المحلية (ريال سعودي، درهم إماراتي، إلخ)
- حساب الضرائب تلقائياً (ضريبة القيمة المضافة 15%)
- تصدير الفواتير إلى PDF مع دعم النصوص العربية
- تتبع حالة الفواتير (مسودة، مرسلة، مدفوعة، متأخرة)

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء والموردين
- تتبع معلومات الاتصال والعناوين
- إدارة الأرقام الضريبية
- تتبع حدود الائتمان
- سجل كامل للمعاملات

### 💰 تتبع المصروفات والإيرادات
- تسجيل المصروفات والإيرادات بتفصيل
- تصنيف المعاملات حسب الفئات
- تتبع طرق الدفع المختلفة
- ربط المعاملات بالمشاريع

### 📈 التقارير المالية
- تقارير الأرباح والخسائر
- الميزانية العمومية
- تقارير التدفق النقدي
- تقارير الضرائب
- تقارير مخصصة حسب الفترة الزمنية

### 👤 إدارة المستخدمين
- نظام أدوار متعدد (مدير، محاسب، أمين صندوق، مشاهد)
- صلاحيات مخصصة لكل دور
- تتبع نشاط المستخدمين
- أمان متقدم للبيانات

### 🗄️ أرشفة المستندات
- رفع وحفظ المستندات المالية
- ربط المستندات بالمعاملات
- نظام بحث متقدم
- نسخ احتياطية آمنة

### 🌍 الدعم المحلي
- واجهة عربية بالكامل (RTL)
- دعم العملات المحلية
- تحويل الأرقام إلى كلمات عربية
- تنسيق التواريخ العربية
- دعم الأرقام الضريبية المحلية

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- Flask 2.3+
- SQLAlchemy 2.0+
- Bootstrap 5.3 (RTL)

### قاعدة البيانات
- SQLite (للتطوير)
- PostgreSQL (للإنتاج)

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/mohasabi.git
cd mohasabi
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate     # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python -c "from app import db; db.create_all()"
```

### 5. تشغيل التطبيق
```bash
python app.py
```

سيعمل التطبيق على العنوان: `http://localhost:5000`

## بيانات تجريبية

للدخول إلى النظام لأول مرة:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## هيكل المشروع

```
mohasabi/
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── config.py              # إعدادات التطبيق
├── utils.py               # وظائف مساعدة
├── requirements.txt       # متطلبات Python
├── templates/             # قوالب HTML
│   ├── base.html
│   ├── dashboard.html
│   ├── login.html
│   ├── customers.html
│   ├── invoices.html
│   └── ...
├── static/                # الملفات الثابتة
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── images/
└── database.db           # قاعدة البيانات (SQLite)
```

## الاستخدام

### إنشاء فاتورة جديدة
1. انتقل إلى "إدارة الفواتير"
2. اضغط على "إنشاء فاتورة جديدة"
3. اختر العميل أو أضف عميل جديد
4. أضف عناصر الفاتورة
5. احفظ الفاتورة أو أرسلها مباشرة

### إضافة عميل جديد
1. انتقل إلى "إدارة العملاء"
2. اضغط على "إضافة عميل جديد"
3. املأ معلومات العميل
4. احفظ البيانات

### عرض التقارير
1. انتقل إلى "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اعرض أو صدّر التقرير

## الأمان

- تشفير كلمات المرور
- حماية CSRF
- جلسات آمنة
- صلاحيات محددة للمستخدمين
- نسخ احتياطية مشفرة

## النسخ الاحتياطية

يدعم النظام النسخ الاحتياطية التلقائية:
- نسخ يومية لقاعدة البيانات
- نسخ أسبوعية للملفات
- تشفير النسخ الاحتياطية
- استعادة سهلة للبيانات

## التطوير

### إضافة ميزة جديدة
1. أنشئ فرع جديد
2. أضف النماذج المطلوبة في `models.py`
3. أضف المسارات في `app.py`
4. أنشئ القوالب في `templates/`
5. اختبر الميزة
6. أرسل طلب دمج

### اختبار التطبيق
```bash
python -m pytest tests/
```

## المساهمة

نرحب بمساهماتكم! يرجى:
1. فورك المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة اختبارات للكود الجديد
4. التأكد من اجتياز جميع الاختبارات
5. إرسال طلب دمج

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم:
- افتح issue في GitHub
- راسلنا على: <EMAIL>
- زر موقعنا: https://mohasabi.com

## خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] تطبيق موبايل
- [ ] API متكامل
- [ ] تكامل مع البنوك
- [ ] ذكاء اصطناعي للتنبؤات
- [ ] تقارير متقدمة
- [ ] دعم عملات إضافية

### المميزات المستقبلية
- [ ] تكامل مع أنظمة ERP
- [ ] دعم المحاسبة الحكومية
- [ ] تطبيق سطح المكتب
- [ ] دعم اللغات الإضافية
- [ ] تحليلات متقدمة

---

**محاسبي** - نظام المحاسبة العربي الشامل 🚀

Made with ❤️ for the Arabic business community
